"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Wallet, ChevronDown } from "lucide-react";
import { formatAddress } from "@/lib/web3-config";

interface CustomConnectButtonProps {
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  className?: string;
}

// Safe wrapper for ConnectButton that handles SSR and provider context
function SafeConnectButton({
  variant,
  size,
  className,
}: {
  variant: "default" | "outline" | "ghost";
  size: "default" | "sm" | "lg";
  className: string;
}) {
  const [ConnectButton, setConnectButton] = useState<any>(null);
  const [error, setError] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const loadConnectButton = async () => {
      try {
        // Add a small delay to ensure Web3Provider is fully mounted
        await new Promise((resolve) => setTimeout(resolve, 100));
        const { ConnectButton: CB } = await import("@rainbow-me/rainbowkit");
        setConnectButton(() => CB);
      } catch (err) {
        console.error("Failed to load ConnectButton:", err);
        setError(true);
      }
    };

    loadConnectButton();
  }, []);

  // Don't render anything until mounted and ConnectButton is loaded
  if (!mounted || error || !ConnectButton) {
    return (
      <Button
        variant={variant}
        size={size}
        className={`gap-2 font-semibold transition-all duration-200 ${className}`}
        disabled
      >
        <Wallet className="h-4 w-4" />
        Connect Wallet
      </Button>
    );
  }

  return (
    <ConnectButton.Custom>
      {({
        account,
        chain,
        openAccountModal,
        openChainModal,
        openConnectModal,
        authenticationStatus,
        mounted,
      }: any) => {
        // Note: If your app doesn't use authentication, you
        // can remove all 'authenticationStatus' checks
        const ready = mounted && authenticationStatus !== "loading";
        const connected =
          ready &&
          account &&
          chain &&
          (!authenticationStatus || authenticationStatus === "authenticated");

        return (
          <div
            {...(!ready && {
              "aria-hidden": true,
              style: {
                opacity: 0,
                pointerEvents: "none",
                userSelect: "none",
              },
            })}
            className={className}
          >
            {(() => {
              if (!connected) {
                return (
                  <Button
                    onClick={openConnectModal}
                    variant={variant}
                    size={size}
                    className={`gap-2 font-semibold transition-all duration-200 ${
                      className || ""
                    }`}
                  >
                    <Wallet className="h-4 w-4" />
                    Connect Wallet
                  </Button>
                );
              }

              if (chain.unsupported) {
                return (
                  <Button
                    onClick={openChainModal}
                    variant="destructive"
                    size={size}
                    className={`gap-2 font-semibold transition-all duration-200 ${
                      className || ""
                    }`}
                  >
                    Wrong network
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                );
              }

              return (
                <div className="flex gap-2">
                  <Button
                    onClick={openChainModal}
                    variant="outline"
                    size={size}
                    className="gap-2 font-medium transition-all duration-200 hover:scale-105"
                  >
                    {chain.hasIcon && (
                      <div
                        style={{
                          background: chain.iconBackground,
                          width: 16,
                          height: 16,
                          borderRadius: 999,
                          overflow: "hidden",
                          marginRight: 4,
                        }}
                      >
                        {chain.iconUrl && (
                          <img
                            alt={chain.name ?? "Chain icon"}
                            src={chain.iconUrl}
                            style={{ width: 16, height: 16 }}
                          />
                        )}
                      </div>
                    )}
                    {chain.name}
                    <ChevronDown className="h-4 w-4" />
                  </Button>

                  <Button
                    onClick={openAccountModal}
                    variant={variant}
                    size={size}
                    className={`gap-2 font-semibold transition-all duration-200 hover:scale-105 ${
                      className || ""
                    }`}
                  >
                    <Wallet className="h-4 w-4" />
                    {formatAddress(account.address)}
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
              );
            })()}
          </div>
        );
      }}
    </ConnectButton.Custom>
  );
}

export function WalletButton({
  variant = "default",
  size = "default",
  className = "",
}: CustomConnectButtonProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Show loading state on server-side and until client-side hydration
  if (!isClient) {
    return (
      <Button
        variant={variant}
        size={size}
        className={`gap-2 font-semibold transition-all duration-200 ${
          className || ""
        }`}
        disabled
      >
        <Wallet className="h-4 w-4" />
        Connect Wallet
      </Button>
    );
  }

  return (
    <SafeConnectButton
      variant={variant}
      size={size}
      className={className || ""}
    />
  );
}

// Simple wallet status component
export function WalletStatus() {
  const [isClient, setIsClient] = useState(false);
  const [ConnectButton, setConnectButton] = useState<any>(null);

  useEffect(() => {
    setIsClient(true);

    const loadConnectButton = async () => {
      try {
        const { ConnectButton: CB } = await import("@rainbow-me/rainbowkit");
        setConnectButton(() => CB);
      } catch (err) {
        console.error("Failed to load ConnectButton for WalletStatus:", err);
      }
    };

    loadConnectButton();
  }, []);

  if (!isClient || !ConnectButton) {
    return (
      <div className="text-sm text-muted-foreground">No wallet connected</div>
    );
  }

  return (
    <ConnectButton.Custom>
      {({ account, chain, mounted }: any) => {
        if (!mounted || !account || !chain) {
          return (
            <div className="text-sm text-muted-foreground">
              No wallet connected
            </div>
          );
        }

        return (
          <div className="flex items-center gap-2 text-sm">
            {chain.hasIcon && (
              <div
                style={{
                  background: chain.iconBackground,
                  width: 16,
                  height: 16,
                  borderRadius: 999,
                  overflow: "hidden",
                }}
              >
                {chain.iconUrl && (
                  <img
                    alt={chain.name ?? "Chain icon"}
                    src={chain.iconUrl}
                    style={{ width: 16, height: 16 }}
                  />
                )}
              </div>
            )}
            <span className="text-muted-foreground">
              {chain.name} • {formatAddress(account.address)}
            </span>
          </div>
        );
      }}
    </ConnectButton.Custom>
  );
}
