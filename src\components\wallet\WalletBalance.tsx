"use client";

import React from "react";
import {
  useAccountSafe,
  useBalanceSafe,
  useChainIdSafe,
} from "@/hooks/useWagmiSafe";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { formatBalance, getChainConfig } from "@/lib/web3-config";
import { Wallet, TrendingUp, Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";

interface WalletBalanceProps {
  className?: string;
}

export function WalletBalance({ className }: WalletBalanceProps) {
  const { address, isConnected } = useAccountSafe();
  const chainId = useChainIdSafe();
  const [showBalance, setShowBalance] = React.useState(true);

  const {
    data: balance,
    isLoading,
    error,
  } = useBalanceSafe({
    address,
  });

  const chainConfig = getChainConfig(chainId);

  if (!isConnected || !address) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Wallet className="h-5 w-5" />
            Wallet Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Wallet className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Connect your wallet to view balance</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Wallet className="h-5 w-5" />
            Wallet Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-destructive">
            <p>Error loading balance</p>
            <p className="text-sm text-muted-foreground mt-1">
              {error.message}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Wallet className="h-5 w-5" />
            Wallet Balance
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowBalance(!showBalance)}
            className="h-8 w-8 p-0"
          >
            {showBalance ? (
              <Eye className="h-4 w-4" />
            ) : (
              <EyeOff className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Network Info */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Network</span>
          <Badge variant="secondary" className="gap-1">
            {chainConfig?.icon} {chainConfig?.name || "Unknown"}
          </Badge>
        </div>

        {/* Balance Display */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Balance</span>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-500">+0.00%</span>
            </div>
          </div>

          {isLoading ? (
            <Skeleton className="h-8 w-32" />
          ) : (
            <div className="text-right">
              <div className="text-2xl font-bold">
                {showBalance ? (
                  <>
                    {formatBalance(balance?.formatted || "0", 6)}{" "}
                    <span className="text-lg text-muted-foreground">
                      {balance?.symbol}
                    </span>
                  </>
                ) : (
                  <span className="text-muted-foreground">••••••</span>
                )}
              </div>
              {showBalance && balance && (
                <div className="text-sm text-muted-foreground">≈ $0.00 USD</div>
              )}
            </div>
          )}
        </div>

        {/* Address Display */}
        <div className="pt-2 border-t">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Address</span>
            <code className="text-xs bg-muted px-2 py-1 rounded">
              {address.slice(0, 6)}...{address.slice(-4)}
            </code>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Compact version for sidebar or header
export function WalletBalanceCompact() {
  const { address, isConnected } = useAccountSafe();
  const { data: balance, isLoading } = useBalanceSafe({
    address,
  });

  if (!isConnected || !address) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 text-sm">
      <Wallet className="h-4 w-4" />
      {isLoading ? (
        <Skeleton className="h-4 w-16" />
      ) : (
        <span>
          {formatBalance(balance?.formatted || "0", 4)} {balance?.symbol}
        </span>
      )}
    </div>
  );
}
