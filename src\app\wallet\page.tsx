"use client";

import React from "react";
import Link from "next/link";
import { useAccountSafe } from "@/hooks/useWagmiSafe";
import { useSupabase } from "@/components/SessionProvider";
import { StaticContentPage } from "@/components/StaticContentPage";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { WalletButton } from "@/components/wallet/WalletButton";
import { WalletBalance } from "@/components/wallet/WalletBalance";
import { WalletProfileIntegration } from "@/components/wallet/WalletProfileIntegration";
import { usePersistentWallet } from "@/hooks/usePersistentWallet";
import {
  Wallet,
  TrendingUp,
  Send,
  Download,
  History,
  AlertTriangle,
  Info,
} from "lucide-react";

export default function WalletPage() {
  const { address, isConnected } = useAccountSafe();
  const { session, profile } = useSupabase();

  // Initialize persistent wallet functionality
  usePersistentWallet();

  return (
    <StaticContentPage title="Crypto Wallet Dashboard">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
            <Wallet className="h-8 w-8" />
            Crypto Wallet Dashboard
          </h1>
          <p className="text-muted-foreground">
            Manage your cryptocurrency wallet and payments for journal services
          </p>
        </div>

        {/* Authentication Check */}
        {!session && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Please login to access your wallet dashboard.
            </AlertDescription>
          </Alert>
        )}

        {session && (
          <>
            {/* Wallet Connection Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5" />
                  Wallet Connection
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!isConnected ? (
                  <div className="text-center space-y-4">
                    <p className="text-muted-foreground">
                      Connect your crypto wallet to get started
                    </p>
                    <WalletButton />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Connected Wallet:</span>
                      <code className="bg-muted px-2 py-1 rounded text-sm">
                        {address?.slice(0, 6)}...{address?.slice(-4)}
                      </code>
                    </div>
                    <WalletButton variant="outline" />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Main Dashboard Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              {/* Wallet Balance */}
              <WalletBalance className="order-1" />

              {/* Profile Integration */}
              <WalletProfileIntegration className="order-2" />
            </div>

            {/* Quick Actions */}
            {isConnected && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 lg:gap-4">
                <Link href="/wallet/send" className="block">
                  <Card className="cursor-pointer hover:shadow-md transition-shadow h-full">
                    <CardContent className="p-4 lg:p-6 text-center space-y-2">
                      <Send className="h-6 w-6 lg:h-8 lg:w-8 mx-auto text-blue-500" />
                      <h3 className="font-semibold text-sm lg:text-base">
                        Send Payment
                      </h3>
                      <p className="text-xs lg:text-sm text-muted-foreground">
                        Send cryptocurrency to other addresses
                      </p>
                    </CardContent>
                  </Card>
                </Link>

                <Link href="/wallet/receive" className="block">
                  <Card className="cursor-pointer hover:shadow-md transition-shadow h-full">
                    <CardContent className="p-4 lg:p-6 text-center space-y-2">
                      <Download className="h-6 w-6 lg:h-8 lg:w-8 mx-auto text-green-500" />
                      <h3 className="font-semibold text-sm lg:text-base">
                        Receive Payment
                      </h3>
                      <p className="text-xs lg:text-sm text-muted-foreground">
                        Get your wallet address to receive payments
                      </p>
                    </CardContent>
                  </Card>
                </Link>

                <Card className="cursor-pointer hover:shadow-md transition-shadow h-full sm:col-span-2 lg:col-span-1">
                  <CardContent className="p-4 lg:p-6 text-center space-y-2">
                    <History className="h-6 w-6 lg:h-8 lg:w-8 mx-auto text-purple-500" />
                    <h3 className="font-semibold text-sm lg:text-base">
                      Transaction History
                    </h3>
                    <p className="text-xs lg:text-sm text-muted-foreground">
                      View your past transactions
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Publication Fees Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Publication Fees
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Coming Soon:</strong> Pay publication fees directly
                    with cryptocurrency. Supported networks: Ethereum, Polygon,
                    and more.
                  </AlertDescription>
                </Alert>

                <div className="mt-4 space-y-2">
                  <h4 className="font-semibold">Supported Payment Methods:</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Ethereum (ETH) - Main network</li>
                    <li>• Polygon (MATIC) - Lower fees</li>
                    <li>• USDC/USDT - Stable coins</li>
                    <li>• More networks coming soon...</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Security Notice */}
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Security Notice:</strong> Always verify transaction
                details before confirming. Never share your private keys or seed
                phrases. This platform only requests wallet connection
                permissions, never your private keys.
              </AlertDescription>
            </Alert>
          </>
        )}
      </div>
    </StaticContentPage>
  );
}
