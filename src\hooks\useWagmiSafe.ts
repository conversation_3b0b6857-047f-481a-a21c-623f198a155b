"use client";

import { useEffect, useState } from "react";
import {
  useAccount,
  useBalance,
  useChainId,
  useConnect,
  useDisconnect,
  useSendTransaction,
} from "wagmi";
import { useWeb3Context } from "@/components/Web3Provider";

/**
 * Safe wrapper for Wagmi hooks that handles provider context issues
 * Returns default values when provider is not available
 */

export function useAccountSafe() {
  const [mounted, setMounted] = useState(false);
  const { isWeb3Available, isLoading } = useWeb3Context();
  const [safeData, setSafeData] = useState({
    address: undefined,
    isConnected: false,
    isConnecting: false,
    isDisconnected: true,
    isReconnecting: false,
    status: "disconnected" as const,
    connector: undefined,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  let wagmiData;
  try {
    // Only use wagmi hooks if Web3 is available
    if (isWeb3Available && !isLoading) {
      wagmiData = useAccount();
    } else {
      wagmiData = safeData;
    }
  } catch (error) {
    // Provider not available, use default values
    wagmiData = safeData;
  }

  useEffect(() => {
    if (mounted && wagmiData && isWeb3Available) {
      setSafeData(wagmiData);
    }
  }, [mounted, wagmiData, isWeb3Available]);

  return mounted && isWeb3Available ? wagmiData : safeData;
}

export function useBalanceSafe(config?: { address?: `0x${string}` }) {
  const [mounted, setMounted] = useState(false);
  const [safeData, setSafeData] = useState({
    data: undefined,
    isError: false,
    isLoading: false,
    error: null,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  let wagmiData;
  try {
    wagmiData = useBalance(config);
  } catch (error) {
    // Provider not available, use default values
    wagmiData = safeData;
  }

  useEffect(() => {
    if (mounted && wagmiData) {
      setSafeData(wagmiData);
    }
  }, [mounted, wagmiData]);

  return mounted ? wagmiData : safeData;
}

export function useChainIdSafe() {
  const [mounted, setMounted] = useState(false);
  const [safeData, setSafeData] = useState(1); // Default to mainnet

  useEffect(() => {
    setMounted(true);
  }, []);

  let wagmiData;
  try {
    wagmiData = useChainId();
  } catch (error) {
    // Provider not available, use default value
    wagmiData = safeData;
  }

  useEffect(() => {
    if (mounted && wagmiData) {
      setSafeData(wagmiData);
    }
  }, [mounted, wagmiData]);

  return mounted ? wagmiData : safeData;
}

export function useConnectSafe() {
  const [mounted, setMounted] = useState(false);
  const [safeData, setSafeData] = useState({
    connect: () => {},
    connectors: [],
    error: null,
    isError: false,
    isPending: false,
    isSuccess: false,
    reset: () => {},
    status: "idle" as const,
    variables: undefined,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  let wagmiData;
  try {
    wagmiData = useConnect();
  } catch (error) {
    // Provider not available, use default values
    wagmiData = safeData;
  }

  useEffect(() => {
    if (mounted && wagmiData) {
      setSafeData(wagmiData);
    }
  }, [mounted, wagmiData]);

  return mounted ? wagmiData : safeData;
}

export function useDisconnectSafe() {
  const [mounted, setMounted] = useState(false);
  const [safeData, setSafeData] = useState({
    disconnect: () => {},
    error: null,
    isError: false,
    isPending: false,
    isSuccess: false,
    reset: () => {},
    status: "idle" as const,
    variables: undefined,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  let wagmiData;
  try {
    wagmiData = useDisconnect();
  } catch (error) {
    // Provider not available, use default values
    wagmiData = safeData;
  }

  useEffect(() => {
    if (mounted && wagmiData) {
      setSafeData(wagmiData);
    }
  }, [mounted, wagmiData]);

  return mounted ? wagmiData : safeData;
}

export function useSendTransactionSafe() {
  const [mounted, setMounted] = useState(false);
  const [safeData, setSafeData] = useState({
    sendTransaction: () => {},
    data: undefined,
    error: null,
    isError: false,
    isPending: false,
    isSuccess: false,
    reset: () => {},
    status: "idle" as const,
    variables: undefined,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  let wagmiData;
  try {
    wagmiData = useSendTransaction();
  } catch (error) {
    // Provider not available, use default values
    wagmiData = safeData;
  }

  useEffect(() => {
    if (mounted && wagmiData) {
      setSafeData(wagmiData);
    }
  }, [mounted, wagmiData]);

  return mounted ? wagmiData : safeData;
}

/**
 * Hook to check if Web3 provider is available and mounted
 */
export function useWeb3Ready() {
  const [mounted, setMounted] = useState(false);
  const [providerReady, setProviderReady] = useState(false);

  useEffect(() => {
    setMounted(true);

    // Check if provider is available
    try {
      useAccount();
      setProviderReady(true);
    } catch (error) {
      setProviderReady(false);
    }
  }, []);

  return mounted && providerReady;
}
