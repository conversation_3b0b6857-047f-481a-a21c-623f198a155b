"use client";

import React, { useEffect, useState } from "react";
import { WagmiProvider } from "wagmi";
import {
  RainbowKitProvider,
  darkTheme,
  lightTheme,
} from "@rainbow-me/rainbowkit";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useTheme } from "next-themes";
import { getWagmiConfig } from "@/lib/web3-config";

// Import RainbowKit styles
import "@rainbow-me/rainbowkit/styles.css";

interface Web3ProviderProps {
  children: React.ReactNode;
}

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});

function RainbowKitThemeProvider({ children }: { children: React.ReactNode }) {
  const { theme } = useTheme();

  return (
    <RainbowKitProvider
      theme={theme === "dark" ? darkTheme() : lightTheme()}
      appInfo={{
        appName: "Jurnal Website",
        learnMoreUrl: "https://rainbowkit.com",
      }}
      modalSize="compact"
    >
      {children}
    </RainbowKitProvider>
  );
}

export function Web3Provider({ children }: Web3ProviderProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Only mount on client side
    if (typeof window !== "undefined") {
      // Add delay to ensure all client-side APIs are available
      const timer = setTimeout(() => {
        setMounted(true);
      }, 200); // Longer delay to ensure everything is ready

      return () => clearTimeout(timer);
    }
  }, []);

  // Server-side rendering: just return children without Web3 providers
  if (typeof window === "undefined") {
    return <>{children}</>;
  }

  // Client-side but not yet mounted: return children without Web3 providers
  if (!mounted) {
    return <>{children}</>;
  }

  // Client-side and mounted: wrap with Web3 providers
  try {
    const config = getWagmiConfig();
    if (!config) {
      // Config not available, return children without Web3 providers
      return <>{children}</>;
    }

    return (
      <WagmiProvider config={config}>
        <QueryClientProvider client={queryClient}>
          <RainbowKitThemeProvider>{children}</RainbowKitThemeProvider>
        </QueryClientProvider>
      </WagmiProvider>
    );
  } catch (error) {
    console.error("Error initializing Web3 providers:", error);
    // Fallback: return children without Web3 providers
    return <>{children}</>;
  }
}

// Hook to check if Web3 is available
export function useWeb3Available() {
  return (
    typeof window !== "undefined" && typeof window.ethereum !== "undefined"
  );
}
