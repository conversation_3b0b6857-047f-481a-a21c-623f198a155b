"use client";

import Link from "next/link";
import Image from "next/image";
import { ModeToggle } from "@/components/ui/mode-toggle";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { MobileNav } from "./MobileNav";
import { useSupabase } from "@/components/SessionProvider";
import { toast } from "sonner";
import { MobileHeaderNav } from "./MobileHeaderNav";
import { WalletButton } from "@/components/wallet/WalletButton";
import { useLogout } from "@/hooks/useLogout";

export function Header() {
  const pathname = usePathname();
  const { session, profile } = useSupabase();
  const { logout } = useLogout();

  const baseNavItems = [
    { name: "HOME", href: "/" },
    { name: "ABOUT", href: "/about" },
    { name: "<PERSON>AR<PERSON>", href: "/search" },
    { name: "CURRENT", href: "/current" },
    { name: "ARCHIVES", href: "/archives" },
    { name: "ANNOUNCEMENTS", href: "/announcements" },
    { name: "FAQ", href: "/faq" },
  ];

  // Filter navItems based on user role for desktop header
  const desktopNavItems =
    profile?.role === "admin"
      ? baseNavItems.filter((item) => item.name !== "FAQ")
      : baseNavItems;

  return (
    <header className="bg-primary text-primary-foreground p-3 shadow-md sticky top-0 z-50 w-full">
      <div className="container mx-auto flex items-center justify-between">
        {/* Left Group: MobileNav, Logo, Journal Info, and E-ISSN */}
        <div className="flex items-center gap-0">
          <MobileNav />
          <Link href="/" className="flex items-center gap-2">
            <Image
              src="/jimeka-logo.png"
              alt="Jurnal Ilmiah Mahasiswa Ekonomi Akuntansi (JIMEKA) Logo Universitas Percobaan Nanda"
              width={35}
              height={35}
              className="rounded-full"
            />
            <div className="flex flex-col w-max">
              {" "}
              {/* Added w-max to make container fit content */}
              <div className="flex justify-between items-baseline w-full">
                {" "}
                {/* Inner div for JIMEKA and E-ISSN */}
                <span className="text-base font-bold">JIMEKA</span>
                <span className="text-[7px] leading-tight text-primary-foreground/80 whitespace-nowrap">
                  E-ISSN: 2581-1002
                </span>{" "}
                {/* Removed mt-1 */}
              </div>
              <span className="text-[7px] md:text-[10px] leading-tight whitespace-nowrap">
                FAKULTAS EKONOMI DAN BISNIS UNIVERSITAS PERCOBAAN NANDA
              </span>{" "}
              {/* Adjusted font size for mobile */}
            </div>
          </Link>
        </div>

        {/* Right Group: Desktop Nav, Crypto Wallet, User Actions, Mode Toggle */}
        <div className="hidden md:flex items-center gap-x-2 lg:gap-x-3">
          {/* Desktop Main Navigation */}
          <nav className="flex items-center gap-x-1 lg:gap-x-2">
            {desktopNavItems.map((item) => (
              <Button
                key={item.name}
                variant="ghost"
                asChild
                className={`text-xs text-primary-foreground hover:bg-primary-foreground/10 transition-colors px-2 lg:px-3 ${
                  pathname === item.href ? "font-bold underline" : ""
                }`}
              >
                <Link href={item.href}>{item.name}</Link>
              </Button>
            ))}
          </nav>

          {/* Separator */}
          <div className="h-6 w-px bg-white/20 mx-1"></div>

          {/* Wallet Connection */}
          <div className="flex items-center">
            <WalletButton
              variant="default"
              size="sm"
              className="bg-white/20 hover:bg-white/30 text-white border-white/30 hover:border-white/40 transition-all duration-200 shadow-lg font-semibold backdrop-blur-sm"
            />
          </div>

          {/* Separator */}
          <div className="h-6 w-px bg-white/20 mx-1"></div>

          {/* Desktop User/Auth Actions */}
          <div className="flex items-center gap-x-1 lg:gap-x-2">
            {session ? (
              <>
                <Button
                  variant="ghost"
                  asChild
                  className={`text-xs text-primary-foreground hover:bg-primary-foreground/10 px-2 lg:px-3 ${
                    pathname === "/profile" ? "font-bold underline" : ""
                  }`}
                >
                  <Link href="/profile">PROFILE</Link>
                </Button>
                {profile?.role === "admin" && (
                  <Button
                    variant="ghost"
                    asChild
                    className={`text-xs text-primary-foreground hover:bg-primary-foreground/10 px-2 lg:px-3 ${
                      pathname === "/admin" ? "font-bold underline" : ""
                    }`}
                  >
                    <Link href="/admin">ADMIN</Link>
                  </Button>
                )}
                <Button
                  variant="ghost"
                  className="text-xs text-primary-foreground hover:bg-primary-foreground/10 px-2 lg:px-3"
                  onClick={logout}
                >
                  LOGOUT
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  asChild
                  className={`text-xs text-primary-foreground hover:bg-primary-foreground/10 px-2 lg:px-3 ${
                    pathname === "/login" ? "font-bold underline" : ""
                  }`}
                >
                  <Link href="/login">LOGIN</Link>
                </Button>
                <Button
                  variant="ghost"
                  asChild
                  className={`text-xs text-primary-foreground hover:bg-primary-foreground/10 px-2 lg:px-3 ${
                    pathname === "/register" ? "font-bold underline" : ""
                  }`}
                >
                  <Link href="/register">REGISTER</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mode Toggle */}
          <ModeToggle />
        </div>

        {/* Mobile Header Nav (only visible on mobile, pushed to far right) */}
        <MobileHeaderNav
          navItems={baseNavItems}
          session={session}
          handleLogout={logout}
        />
      </div>
    </header>
  );
}
