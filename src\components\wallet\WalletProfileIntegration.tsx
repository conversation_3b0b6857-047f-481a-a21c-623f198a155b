"use client";

import React, { useState, useEffect } from "react";
import { useAccountSafe, useDisconnectSafe } from "@/hooks/useWagmiSafe";
import { useSupabase } from "@/components/SessionProvider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { WalletButton } from "./WalletButton";
import { formatAddress } from "@/lib/web3-config";
import { toast } from "sonner";
import {
  Wallet,
  Link as LinkIcon,
  Unlink,
  CheckCircle,
  AlertTriangle,
  Loader2,
} from "lucide-react";

interface WalletProfileIntegrationProps {
  className?: string;
}

export function WalletProfileIntegration({
  className,
}: WalletProfileIntegrationProps) {
  const { address, isConnected } = useAccountSafe();
  const { disconnect } = useDisconnectSafe();
  const { supabase, session, profile } = useSupabase();
  const [isLinking, setIsLinking] = useState(false);
  const [isUnlinking, setIsUnlinking] = useState(false);

  // Check if current wallet is linked to profile
  const isWalletLinked = profile?.wallet_address === address;
  const hasLinkedWallet = !!profile?.wallet_address;

  const handleLinkWallet = async () => {
    if (!session || !address) {
      toast.error("Please login and connect your wallet first");
      return;
    }

    setIsLinking(true);
    try {
      const { error } = await supabase
        .from("profiles")
        .update({ wallet_address: address })
        .eq("id", session.user.id);

      if (error) {
        throw error;
      }

      toast.success("Wallet successfully linked to your profile!");
      // Refresh the page to update the profile data
      window.location.reload();
    } catch (error: any) {
      console.error("Error linking wallet:", error);
      toast.error(`Failed to link wallet: ${error.message}`);
    } finally {
      setIsLinking(false);
    }
  };

  const handleUnlinkWallet = async () => {
    if (!session) {
      toast.error("Please login first");
      return;
    }

    setIsUnlinking(true);
    try {
      const { error } = await supabase
        .from("profiles")
        .update({ wallet_address: null })
        .eq("id", session.user.id);

      if (error) {
        throw error;
      }

      toast.success("Wallet successfully unlinked from your profile!");
      // Optionally disconnect the wallet
      disconnect();
      // Refresh the page to update the profile data
      window.location.reload();
    } catch (error: any) {
      console.error("Error unlinking wallet:", error);
      toast.error(`Failed to unlink wallet: ${error.message}`);
    } finally {
      setIsUnlinking(false);
    }
  };

  if (!session) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Wallet Integration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Please login to link your crypto wallet to your profile.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wallet className="h-5 w-5" />
          Wallet Integration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Wallet Connection Status */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Current Wallet</span>
            {isConnected ? (
              <Badge variant="default" className="gap-1">
                <CheckCircle className="h-3 w-3" />
                Connected
              </Badge>
            ) : (
              <Badge variant="secondary">Not Connected</Badge>
            )}
          </div>

          {isConnected && address ? (
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <code className="text-sm">{formatAddress(address, 6)}</code>
              <WalletButton variant="outline" size="sm" />
            </div>
          ) : (
            <div className="flex justify-center">
              <WalletButton />
            </div>
          )}
        </div>

        {/* Profile Linked Wallet Status */}
        <div className="space-y-3 pt-4 border-t">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Profile Linked Wallet</span>
            {hasLinkedWallet ? (
              <Badge variant="default" className="gap-1">
                <LinkIcon className="h-3 w-3" />
                Linked
              </Badge>
            ) : (
              <Badge variant="outline">Not Linked</Badge>
            )}
          </div>

          {hasLinkedWallet && (
            <div className="p-3 bg-muted rounded-lg">
              <code className="text-sm">
                {formatAddress(profile.wallet_address!, 6)}
              </code>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-2 pt-4 border-t">
          {isConnected && address ? (
            <>
              {!isWalletLinked && (
                <Button
                  onClick={handleLinkWallet}
                  disabled={isLinking}
                  className="w-full gap-2"
                >
                  {isLinking ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <LinkIcon className="h-4 w-4" />
                  )}
                  {isLinking ? "Linking..." : "Link Wallet to Profile"}
                </Button>
              )}

              {isWalletLinked && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    This wallet is linked to your profile. You can now receive
                    crypto payments.
                  </AlertDescription>
                </Alert>
              )}

              {hasLinkedWallet && (
                <Button
                  onClick={handleUnlinkWallet}
                  disabled={isUnlinking}
                  variant="outline"
                  className="w-full gap-2"
                >
                  {isUnlinking ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Unlink className="h-4 w-4" />
                  )}
                  {isUnlinking ? "Unlinking..." : "Unlink Wallet"}
                </Button>
              )}
            </>
          ) : (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Connect your wallet first to link it to your profile.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Information */}
        <div className="text-xs text-muted-foreground pt-2 border-t">
          <p>
            Linking your wallet allows you to receive cryptocurrency payments
            for publication fees and other transactions directly to your
            account.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
